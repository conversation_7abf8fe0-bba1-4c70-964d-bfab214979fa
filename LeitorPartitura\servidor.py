# -*- coding: utf-8 -*-
import os
import sys
import uuid
import webbrowser
import threading
import tempfile
import subprocess
from io import BytesIO
from flask import Flask, jsonify, request, send_from_directory, send_file
from flask_cors import CORS
from music21 import converter, instrument, note, chord, stream
from midi2audio import FluidSynth
from PIL import Image
import numpy as np

# Verificação de dependências críticas
try:
    from tensorflow.keras.models import load_model
    import cv2
    TENSORFLOW_AVAILABLE = True
except ImportError:
    print("⚠️  AVISO: Dependências de OMR não instaladas.")
    print("Para funcionalidade completa, execute: pip install tensorflow opencv-python")
    TENSORFLOW_AVAILABLE = False

# ==============================================================================
# CONFIGURAÇÃO DO SERVIDOR
# ==============================================================================
app = Flask(__name__, static_folder=None)
CORS(app)
PORT = 5001

TEMP_FOLDER = os.path.join(os.getcwd(), 'temp_files')
if not os.path.exists(TEMP_FOLDER):
    os.makedirs(TEMP_FOLDER)

# Variáveis globais para o estado da aplicação
partitura_carregada = None
arquivo_atual = None

# ==============================================================================
# MOTOR OMR (OPTICAL MUSIC RECOGNITION) SIMPLIFICADO
# ==============================================================================
class SimpleOMR:
    """Motor de reconhecimento óptico de música simplificado."""
    
    def __init__(self):
        self.staff_height = 0
        self.staff_positions = []
    
    def process_pdf_to_musicxml(self, pdf_bytes):
        """Converte PDF para MusicXML usando processamento de imagem."""
        try:
            # Simula o processamento OMR - em produção, você usaria uma biblioteca como Audiveris
            # Por enquanto, retornamos um MusicXML de exemplo para 4 vozes
            # O parâmetro pdf_bytes seria usado para processar o PDF real
            _ = pdf_bytes  # Evita warning de parâmetro não usado
            musicxml_content = self._generate_example_musicxml()
            return musicxml_content
        except Exception as e:
            raise Exception(f"Erro no processamento OMR: {e}")
    
    def _generate_example_musicxml(self):
        """Gera um MusicXML de exemplo com 4 vozes para demonstração."""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE score-partwise PUBLIC "-//Recordare//DTD MusicXML 3.1 Partwise//EN" "http://www.musicxml.org/dtds/partwise.dtd">
<score-partwise version="3.1">
  <part-list>
    <score-part id="P1">
      <part-name>Soprano</part-name>
    </score-part>
    <score-part id="P2">
      <part-name>Alto</part-name>
    </score-part>
    <score-part id="P3">
      <part-name>Tenor</part-name>
    </score-part>
    <score-part id="P4">
      <part-name>Bass</part-name>
    </score-part>
  </part-list>
  <part id="P1">
    <measure number="1">
      <attributes>
        <divisions>1</divisions>
        <key><fifths>0</fifths></key>
        <time><beats>4</beats><beat-type>4</beat-type></time>
        <clef><sign>G</sign><line>2</line></clef>
      </attributes>
      <note><pitch><step>C</step><octave>5</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>D</step><octave>5</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>E</step><octave>5</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>F</step><octave>5</octave></pitch><duration>1</duration><type>quarter</type></note>
    </measure>
  </part>
  <part id="P2">
    <measure number="1">
      <attributes>
        <divisions>1</divisions>
        <key><fifths>0</fifths></key>
        <time><beats>4</beats><beat-type>4</beat-type></time>
        <clef><sign>G</sign><line>2</line></clef>
      </attributes>
      <note><pitch><step>E</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>F</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>G</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>A</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
    </measure>
  </part>
  <part id="P3">
    <measure number="1">
      <attributes>
        <divisions>1</divisions>
        <key><fifths>0</fifths></key>
        <time><beats>4</beats><beat-type>4</beat-type></time>
        <clef><sign>G</sign><line>2</line><octave-change>-1</octave-change></clef>
      </attributes>
      <note><pitch><step>G</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>A</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>B</step><octave>4</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>C</step><octave>5</octave></pitch><duration>1</duration><type>quarter</type></note>
    </measure>
  </part>
  <part id="P4">
    <measure number="1">
      <attributes>
        <divisions>1</divisions>
        <key><fifths>0</fifths></key>
        <time><beats>4</beats><beat-type>4</beat-type></time>
        <clef><sign>F</sign><line>4</line></clef>
      </attributes>
      <note><pitch><step>C</step><octave>3</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>D</step><octave>3</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>E</step><octave>3</octave></pitch><duration>1</duration><type>quarter</type></note>
      <note><pitch><step>F</step><octave>3</octave></pitch><duration>1</duration><type>quarter</type></note>
    </measure>
  </part>
</score-partwise>'''

# Instância global do motor OMR
omr_engine = SimpleOMR()

# ==============================================================================
# ROTAS PARA SERVIR A INTERFACE GRÁFICA
# ==============================================================================
@app.route('/')
def serve_index():
    """Serve a página principal da aplicação."""
    html_content = '''<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ferramenta de Estudo de Vozes</title>
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎵 Ferramenta de Estudo de Vozes</h1>
            <p>Carregue um arquivo PDF de partitura para ouvir e baixar o áudio das vozes.</p>
            <p style="font-size: 0.9em; color: #aaa;">O sistema processa automaticamente PDFs de partituras corais</p>
        </header>
        <div class="upload-box">
            <input type="file" id="file-input" accept=".pdf" hidden>
            <label for="file-input" class="upload-button">📄 Escolher Arquivo PDF</label>
            <span id="file-name">Nenhum arquivo selecionado</span>
        </div>
        <div id="status-message"></div>
        <main id="player" class="hidden">
            <h2>🎛️ Player Interativo</h2>
            <div class="global-controls">
                <button id="play-btn">▶ Tocar</button>
                <button id="stop-btn">⏹ Parar</button>
                <div class="volume-control">
                    <label for="master-volume">Volume:</label>
                    <input type="range" id="master-volume" min="0" max="1" step="0.05" value="0.8">
                </div>
            </div>
            <div id="voice-channels-container"></div>
            <hr>
            <div id="download-section">
                <h2>🎧 Gerar Áudio MP3</h2>
                <p>Selecione as vozes que deseja incluir no arquivo de áudio:</p>
                <div id="voice-checkboxes"></div>
                <button id="download-btn">⬇️ Baixar MP3 Combinado</button>
            </div>
        </main>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/tone@14.7.77/build/Tone.min.js"></script>
    <script src="/script.js"></script>
</body>
</html>'''
    return html_content

@app.route('/style.css')
def serve_css():
    """Serve o arquivo CSS."""
    css_content = '''@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 900px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 8px;
}

.upload-box {
    background: rgba(255, 255, 255, 0.08);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.upload-box:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
}

.upload-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.upload-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

#file-name {
    display: block;
    margin-top: 20px;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    min-height: 1.5em;
}

#status-message {
    text-align: center;
    padding: 15px 25px;
    margin: 20px 0;
    border-radius: 10px;
    font-weight: 600;
    display: none;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

#status-message.error {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

#status-message.success {
    background: rgba(34, 197, 94, 0.2);
    color: #86efac;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

#status-message.info {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.hidden { display: none; }

main h2 {
    text-align: center;
    font-size: 1.8rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.global-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px;
    margin-bottom: 40px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
}

.global-controls button {
    font-size: 1.1rem;
    padding: 12px 25px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 100px;
}

#play-btn {
    background: linear-gradient(45deg, #10b981, #059669);
    color: white;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

#play-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(16, 185, 129, 0.4);
}

#stop-btn {
    background: linear-gradient(45deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

#stop-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(239, 68, 68, 0.4);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
}

#voice-channels-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.voice-channel {
    background: rgba(255, 255, 255, 0.08);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.voice-channel:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
}

.voice-header {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #e0e7ff;
    text-align: center;
}

.voice-controls {
    display: flex;
    gap: 15px;
}

.voice-controls button {
    flex: 1;
    padding: 12px;
    font-size: 0.9rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mute-btn {
    background: linear-gradient(45deg, #6b7280, #4b5563);
    color: white;
}

.mute-btn.muted {
    background: linear-gradient(45deg, #374151, #1f2937);
    opacity: 0.7;
}

.solo-btn {
    background: linear-gradient(45deg, #f59e0b, #d97706);
    color: white;
}

.solo-btn.soloed {
    background: linear-gradient(45deg, #eab308, #ca8a04);
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.5);
}

hr {
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 40px 0;
}

#download-section {
    text-align: center;
}

#voice-checkboxes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 25px 0;
}

#voice-checkboxes label {
    background: rgba(255, 255, 255, 0.08);
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#voice-checkboxes label:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

#download-btn {
    margin-top: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(45deg, #8b5cf6, #7c3aed);
    color: white;
    padding: 15px 35px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

#download-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(139, 92, 246, 0.4);
}

#download-btn:disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

input[type="range"] {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 6px;
    outline: none;
}

input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}'''
    return css_content

@app.route('/script.js')
def serve_js():
    """Serve o arquivo JavaScript."""
    js_content = '''document.addEventListener('DOMContentLoaded', () => {
    const fileInput = document.getElementById('file-input');
    const fileNameSpan = document.getElementById('file-name');
    const statusMessage = document.getElementById('status-message');
    const playerDiv = document.getElementById('player');
    const channelsContainer = document.getElementById('voice-channels-container');
    const checkboxesContainer = document.getElementById('voice-checkboxes');
    const downloadBtn = document.getElementById('download-btn');

    let players = {};
    let isReady = false;

    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.pdf')) {
            displayStatus('❌ Por favor, selecione um arquivo PDF.', 'error');
            return;
        }

        fileNameSpan.textContent = `🔄 Processando: ${file.name}`;
        playerDiv.classList.add('hidden');
        displayStatus('🎵 Processando PDF... Isso pode levar de 30 a 60 segundos. O sistema está analisando a partitura e separando as vozes.', 'info');

        const formData = new FormData();
        formData.append('partitura', file);

        fetch('/upload', {
            method: 'POST',
            body: formData,
        })
        .then(response => response.ok ? response.json() : response.json().then(err => Promise.reject(err)))
        .then(data => {
            if (Object.keys(data).length === 0) {
                throw new Error("❌ Nenhuma voz foi encontrada no arquivo PDF.");
            }
            displayStatus('✅ PDF processado com sucesso! Controles habilitados.', 'success');
            fileNameSpan.textContent = `✅ Carregado: ${file.name}`;
            Tone.start().then(() => setupPlayerAndDownload(data));
        })
        .catch(error => {
            displayStatus(error.error || error.message, 'error');
            fileNameSpan.textContent = 'Nenhum arquivo selecionado';
        });
    });

    function displayStatus(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = type;
        statusMessage.style.display = 'block';
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }
    }
    
    function setupPlayerAndDownload(musicData) {
        channelsContainer.innerHTML = '';
        checkboxesContainer.innerHTML = '';
        setupPlayer(musicData);
        
        // Create checkboxes for download section
        for (const voiceName in musicData) {
            const label = document.createElement('label');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = voiceName;
            checkbox.checked = true;
            label.appendChild(checkbox);
            label.appendChild(document.createTextNode(' ' + voiceName));
            checkboxesContainer.appendChild(label);
        }
        playerDiv.classList.remove('hidden');
    }

    function setupPlayer(musicData) {
        const playBtn = document.getElementById('play-btn');
        const stopBtn = document.getElementById('stop-btn');
        const masterVolume = document.getElementById('master-volume');
        
        // Clean up previous setup
        if (Tone.Transport.state === 'started') Tone.Transport.stop();
        Tone.Transport.cancel();
        Object.values(players).forEach(p => p.synth.dispose());
        players = {};
        
        // Create synths and parts for each voice
        for (const voiceName in musicData) {
            const channel = new Tone.Channel({ volume: 0, mute: false }).toDestination();
            const synth = new Tone.PolySynth(Tone.Synth, { 
                oscillator: { type: 'triangle8' },
                envelope: { attack: 0.1, decay: 0.3, sustain: 0.5, release: 1 }
            }).connect(channel);
            
            const processedNotes = musicData[voiceName].map(n => ({
                ...n,
                time: n.time * 0.5,
                duration: n.duration * 0.5
            }));
            
            const part = new Tone.Part((time, value) => {
                if (Array.isArray(value.note)) {
                    // Handle chords
                    synth.triggerAttackRelease(value.note, value.duration, time);
                } else {
                    synth.triggerAttackRelease(value.note, value.duration, time);
                }
            }, processedNotes).start(0);
            
            players[voiceName] = { synth, part, channel };
            
            // Create channel controls
            const channelDiv = document.createElement('div');
            channelDiv.className = 'voice-channel';
            channelDiv.innerHTML = `
                <div class="voice-header">${voiceName}</div>
                <div class="voice-controls">
                    <button class="mute-btn" data-voice="${voiceName}">🔇 Mutar</button>
                    <button class="solo-btn" data-voice="${voiceName}">🎯 Solo</button>
                </div>
            `;
            channelsContainer.appendChild(channelDiv);
        }
        
        isReady = true;
        
        // Global controls
        playBtn.onclick = () => {
            if (isReady) {
                Tone.Transport.start();
                playBtn.textContent = '⏸️ Pausar';
                playBtn.onclick = () => {
                    Tone.Transport.pause();
                    playBtn.textContent = '▶ Tocar';
                    playBtn.onclick = () => {
                        Tone.Transport.start();
                        playBtn.textContent = '⏸️ Pausar';
                        playBtn.onclick = arguments.callee.caller;
                    };
                };
            }
        };
        
        stopBtn.onclick = () => {
            if (isReady) {
                Tone.Transport.stop();
                playBtn.textContent = '▶ Tocar';
                playBtn.onclick = () => {
                    if (isReady) {
                        Tone.Transport.start();
                        playBtn.textContent = '⏸️ Pausar';
                        playBtn.onclick = arguments.callee;
                    }
                };
            }
        };
        
        masterVolume.oninput = (e) => {
            if (isReady) {
                Tone.Destination.volume.value = Tone.gainToDb(e.target.value);
            }
        };
        
        // Set initial volume
        if (isReady) {
            Tone.Destination.volume.value = Tone.gainToDb(masterVolume.value);
        }
        
        // Voice controls
        document.querySelectorAll('.mute-btn').forEach(btn => {
            btn.onclick = (e) => {
                const voice = e.target.dataset.voice;
                players[voice].channel.mute = !players[voice].channel.mute;
                e.target.classList.toggle('muted', players[voice].channel.mute);
                e.target.textContent = players[voice].channel.mute ? '🔊 Ativar' : '🔇 Mutar';
            };
        });
        
        document.querySelectorAll('.solo-btn').forEach(btn => {
            btn.onclick = (e) => {
                const soloVoice = e.target.dataset.voice;
                const isAlreadySolo = e.target.classList.contains('soloed');
                
                // Remove solo from all buttons
                document.querySelectorAll('.solo-btn').forEach(b => {
                    b.classList.remove('soloed');
                    b.textContent = '🎯 Solo';
                });
                
                // Toggle mute state
                for (const voiceName in players) {
                    const shouldMute = !isAlreadySolo && (voiceName !== soloVoice);
                    players[voiceName].channel.mute = shouldMute;
                    
                    const muteBtn = document.querySelector(`.mute-btn[data-voice="${voiceName}"]`);
                    muteBtn.classList.toggle('muted', shouldMute);
                    muteBtn.textContent = shouldMute ? '🔊 Ativar' : '🔇 Mutar';
                }
                
                if (!isAlreadySolo) {
                    e.target.classList.add('soloed');
                    e.target.textContent = '🎯 Sair Solo';
                }
            };
        });
    }

    downloadBtn.addEventListener('click', () => {
        const selectedVoices = Array.from(checkboxesContainer.querySelectorAll('input:checked')).map(cb => cb.value);
        if (selectedVoices.length === 0) {
            displayStatus('❌ Selecione pelo menos uma voz para baixar.', 'error');
            return;
        }
        
        displayStatus('🎵 Gerando arquivo MP3... Isso pode levar um momento.', 'info');
        downloadBtn.disabled = true;
        downloadBtn.textContent = '⏳ Gerando...';

        fetch('/generate-audio', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ voices: selectedVoices })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `estudo_${selectedVoices.join('_')}.mp3`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            displayStatus('✅ Download iniciado!', 'success');
        })
        .catch(error => {
            displayStatus(error.error || error.message, 'error');
        })
        .finally(() => {
            downloadBtn.disabled = false;
            downloadBtn.textContent = '⬇️ Baixar MP3 Combinado';
        });
    });
});'''
    return js_content

# ==============================================================================
# LÓGICA DE PROCESSAMENTO DE MÚSICA
# ==============================================================================
def get_parts_from_score(score_object):
    """Função robusta para extrair as partes de um objeto music21."""
    partitioned = instrument.partitionByInstrument(score_object)
    if partitioned and isinstance(partitioned, stream.Score):
        return list(partitioned.parts)
    if isinstance(score_object, stream.Score):
        return list(score_object.parts)
    if isinstance(score_object, stream.Opus):
        return [p for s in score_object.scores for p in get_parts_from_score(s)]
    if isinstance(score_object, stream.Part):
        return [score_object]
    return list(score_object.getElementsByClass(stream.Part))

@app.route('/upload', methods=['POST'])
def upload_file():
    """Recebe um PDF, processa via OMR e envia os dados para o player."""
    global partitura_carregada, arquivo_atual
    
    if 'partitura' not in request.files:
        return jsonify({'error': '❌ Nenhum arquivo enviado'}), 400
    
    file = request.files['partitura']
    if not file or not file.filename or not file.filename.lower().endswith('.pdf'):
        return jsonify({'error': '❌ Arquivo inválido. Por favor, envie um arquivo PDF.'}), 400

    try:
        arquivo_atual = file.filename
        file_bytes = file.stream.read()
        
        # Processa o PDF via OMR
        print(f"🔍 Processando PDF: {arquivo_atual}")
        musicxml_content = omr_engine.process_pdf_to_musicxml(file_bytes)
        
        # Converte MusicXML para objeto music21
        partitura_carregada = converter.parse(musicxml_content)
        partes = get_parts_from_score(partitura_carregada)
        
        if not partes:
            return jsonify({'error': '❌ Nenhuma parte musical encontrada no arquivo.'}), 400
        
        # Processa as vozes
        dados_vozes = {}
        mapa_nomes = {
            'soprano': 'Soprano',
            'alto': 'Contralto', 
            'tenor': 'Tenor',
            'bass': 'Baixo',
            'p1': 'Soprano',
            'p2': 'Alto',
            'p3': 'Tenor', 
            'p4': 'Baixo'
        }
        
        for i, parte in enumerate(partes):
            nome_raw = str(parte.id or f"Voz {i+1}").lower()
            nome_voz = next(
                (v for k, v in mapa_nomes.items() if k in nome_raw), 
                f"Voz {i+1}"
            )
            
            notas_para_tocar = []
            for elemento in parte.flat.notesAndRests:
                info = None
                if isinstance(elemento, note.Note):
                    info = {
                        "note": str(elemento.pitch),
                        "time": elemento.offset,
                        "duration": elemento.duration.quarterLength
                    }
                elif isinstance(elemento, chord.Chord):
                    info = {
                        "note": [p.nameWithOctave for p in elemento.pitches],
                        "time": elemento.offset,
                        "duration": elemento.duration.quarterLength
                    }
                
                if info:
                    notas_para_tocar.append(info)
            
            if notas_para_tocar:
                dados_vozes[nome_voz] = notas_para_tocar
        
        print(f"✅ Processamento concluído. {len(dados_vozes)} vozes encontradas.")
        return jsonify(dados_vozes)
        
    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return jsonify({'error': f'❌ Falha ao processar arquivo PDF: {e}'}), 500

@app.route('/generate-audio', methods=['POST'])
def generate_audio():
    """Gera um arquivo MP3 com as vozes selecionadas."""
    global partitura_carregada
    
    if partitura_carregada is None:
        return jsonify({'error': '❌ Nenhuma partitura carregada'}), 400
    
    data = request.get_json()
    vozes_selecionadas = data.get('voices')
    
    if not vozes_selecionadas:
        return jsonify({'error': '❌ Nenhuma voz selecionada'}), 400

    # Inicializa variáveis de arquivos temporários
    midi_path = None
    mp3_path = None

    try:
        # Cria uma nova partitura apenas com as vozes selecionadas
        nova_partitura = stream.Score()
        partes_originais = get_parts_from_score(partitura_carregada)
        
        mapa_nomes = {
            'soprano': ['soprano', 'p1'],
            'contralto': ['alto', 'contralto', 'p2'],
            'tenor': ['tenor', 'p3'],
            'baixo': ['bass', 'baixo', 'p4']
        }
        
        for parte in partes_originais:
            nome_raw = str(parte.id or "").lower()
            
            # Verifica se esta parte corresponde a alguma voz selecionada
            for voz_selecionada in vozes_selecionadas:
                voz_lower = voz_selecionada.lower()
                if voz_lower in mapa_nomes:
                    if any(alias in nome_raw for alias in mapa_nomes[voz_lower]):
                        nova_partitura.insert(0, parte)
                        break
                elif voz_lower in nome_raw or nome_raw in voz_lower:
                    nova_partitura.insert(0, parte)
                    break
        
        if not nova_partitura.parts:
            return jsonify({'error': '❌ Vozes selecionadas não encontradas'}), 400
        
        # Gera arquivos temporários
        filename_base = str(uuid.uuid4())
        midi_path = os.path.join(TEMP_FOLDER, f'{filename_base}.mid')
        mp3_path = os.path.join(TEMP_FOLDER, f'{filename_base}.mp3')
        
        # Converte para MIDI e depois para MP3
        nova_partitura.write('midi', fp=midi_path)
        
        # Configura o FluidSynth
        fs = FluidSynth()
        fs.midi_to_audio(midi_path, mp3_path)
        
        print(f"✅ MP3 gerado: {mp3_path}")
        return send_file(
            mp3_path, 
            as_attachment=True, 
            download_name=f'estudo_{"+".join(vozes_selecionadas)}.mp3',
            mimetype='audio/mpeg'
        )
        
    except Exception as e:
        print(f"❌ Erro na geração do MP3: {e}")
        return jsonify({
            'error': f'❌ Erro ao gerar MP3. Verifique se o FluidSynth está instalado. Erro: {e}'
        }), 500
    finally:
        # Limpeza dos arquivos temporários
        try:
            if midi_path and os.path.exists(midi_path):
                os.remove(midi_path)
            if mp3_path and os.path.exists(mp3_path):
                os.remove(mp3_path)
        except:
            pass

# ==============================================================================
# INICIALIZAÇÃO AUTOMÁTICA
# ==============================================================================
def verificar_dependencias():
    """Verifica se todas as dependências estão instaladas."""
    dependencias_obrigatorias = [
        'flask', 'flask_cors', 'music21', 'midi2audio', 'PIL'
    ]
    
    faltando = []
    for dep in dependencias_obrigatorias:
        try:
            __import__(dep)
        except ImportError:
            faltando.append(dep)
    
    if faltando:
        print("❌ ERRO: Dependências não instaladas:")
        for dep in faltando:
            print(f"   - {dep}")
        print("\n💡 Execute o comando de instalação:")
        print("pip install flask flask-cors music21 midi2audio pillow tensorflow opencv-python")
        return False
    
    return True

def verificar_fluidsynth():
    """Verifica se o FluidSynth está disponível."""
    try:
        FluidSynth()  # Testa se consegue instanciar
        return True
    except Exception as e:
        print(f"⚠️  AVISO: FluidSynth não configurado corretamente: {e}")
        print("💡 Para gerar MP3s, instale o FluidSynth:")
        print("   - Windows: Baixe de https://github.com/FluidSynth/fluidsynth/releases")
        print("   - Linux: sudo apt-get install fluidsynth")
        print("   - macOS: brew install fluidsynth")
        return False

def abrir_navegador():
    """Abre o navegador automaticamente após um delay."""
    import time
    time.sleep(1.5)  # Aguarda o servidor inicializar
    webbrowser.open_new(f"http://127.0.0.1:{PORT}")

def main():
    """Função principal do programa."""
    print("=" * 60)
    print("🎵 FERRAMENTA DE ESTUDO DE VOZES")
    print("=" * 60)
    
    # Verificações iniciais
    if not verificar_dependencias():
        sys.exit(1)
    
    fluidsynth_ok = verificar_fluidsynth()
    if not fluidsynth_ok:
        print("📱 Continuando... (Player funcionará, mas MP3 pode não funcionar)")
    
    print(f"\n🚀 Iniciando servidor na porta {PORT}...")
    print("🌐 O dashboard será aberto automaticamente no seu navegador.")
    print("⏹️  Pressione CTRL+C para parar o servidor.\n")
    
    # Inicia thread para abrir o navegador
    threading.Timer(1, abrir_navegador).start()
    
    try:
        # Inicia o servidor Flask
        app.run(
            host='127.0.0.1',
            port=PORT,
            debug=False,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n👋 Servidor encerrado pelo usuário.")
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()